package org.galiasystems.csms.graphql;

import io.netty.handler.codec.http.HttpResponseStatus;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.vertx.VertxContextSupport;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import org.galiasystems.csms.cs.adapters.ocpp.OCPPVersion;
import org.galiasystems.csms.cs.adapters.ocpp.TestChargerFactory;
import org.galiasystems.csms.management.model.enums.AvailabilityStatus;
import org.galiasystems.csms.management.model.enums.ChargingStationStatus;
import org.galiasystems.csms.management.model.enums.EvseStatus;
import org.galiasystems.csms.management.model.enums.ResetType;
import org.galiasystems.csms.management.types.enums.RequestStartStopTransactionStatus;
import org.galiasystems.csms.management.types.enums.ResetStatus;
import org.galiasystems.csms.test.utils.DatabaseResetUtil;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static io.restassured.RestAssured.given;
import static org.galiasystems.csms.management.types.enums.RequestStartStopTransactionStatus.Accepted;
import static org.hamcrest.CoreMatchers.*;
import static org.hamcrest.Matchers.greaterThanOrEqualTo;
import static org.hamcrest.Matchers.hasSize;

@QuarkusTest
public class ChargingStationResolverTest {

    private static final String GRAPHQL_ENDPOINT = "/graphql";

    private static final String CHARGING_STATION_BASIC_QUERY = """
            {
              "query": "query {
                chargingStation(id: %d) {
                  id
                  name
                  status
                  availabilityStatus
                }
              }"
            }""";

    private static final String CHARGING_STATION_WITH_EVSES_QUERY = """
            {
              "query": "query {
                chargingStation(id: %d) {
                  id
                  name
                  evses {
                    id
                    chargingStationEvseId
                    status
                    availabilityStatus
                  }
                }
              }"
            }""";

    private static final String CHARGING_STATION_SIMPLE_QUERY = """
            {
              "query": "query {
                chargingStation(id: %d) {
                  id
                  name
                }
              }"
            }""";

    private static final String CHARGING_STATIONS_BASIC_QUERY = """
            {
              "query": "query {
                chargingStations {
                  id
                  name
                  status
                  availabilityStatus
                }
              }"
            }""";

    private static final String CHARGING_STATIONS_WITH_FILTER_QUERY = """
            {
              "query": "query {
                chargingStations(filter: {%s}) {
                  id
                  name
                  status
                  availabilityStatus
                }
              }"
            }""";

    private static final String CHARGING_STATIONS_WITH_NESTED_QUERY = """
            {
              "query": "query {
                chargingStations {
                  id
                  name
                  status
                  availabilityStatus
                  reports {
                    id
                    requestId
                    status
                    type
                    responseStatus
                  }
                  chargingStationVariables {
                    id
                    componentName
                    variableName
                    chargingStationVariableValues {
                      id
                      type
                      value
                      mutability
                    }
                  }
                  evses {
                    id
                    chargingStationEvseId
                    status
                    availabilityStatus
                  }
                }
              }"
            }""";

    private static final String REGISTER_CHARGING_STATION_MUTATION = """
            {
              "query": "mutation {
                registerChargingStation(name: \\"%s\\") {
                  id
                  name
                  userName
                  password
                  status
                  availabilityStatus
                  url
                }
              }"
            }""";

    private static final String UPDATE_CHARGING_STATION_MUTATION = """
            {
              "query": "mutation {
                updateChargingStation(chargingStationId: %d, name: \\"%s\\") {
                  id
                  name
                  status
                  availabilityStatus
                  url
                  userName
                  password
                }
              }"
            }""";

    private static final String CHANGE_AVAILABILITY_MUTATION = """
            {
              "query": "mutation {
                changeAvailability(chargingStationId: %d, evseId: %s, connectorId: %s, availabilityStatus: %s) {
                  resultStatus
                  reasonCode
                  additionalInfo
                }
              }"
            }""";

    private static final String RESET_MUTATION = """
            {
              "query": "mutation {
                reset(id: %d, evseId: %s, resetType: %s) {
                  resetStatus
                  reasonCode
                  additionalInfo
                }
              }"
            }""";

    private static final String REQUEST_START_TRANSACTION_MUTATION = """
            {
              "query": "mutation {
                            requestStartTransaction(id: %d, evseId: %s) {
                              status
                            }
                          }"
            }""";

    private static final String REQUEST_STOP_TRANSACTION_MUTATION = """
            {
              "query": "mutation {
                            requestStopTransaction(id: %d, transactionId: \\"%s\\") {
                              status
                            }
                          }"
            }""";

    private static final String DIAGNOSTICS_QUERY = """
            {
              "query": "query {
                diagnostics(chargingStationId: %d, location: \\"%s\\"
                %s) {
                  fileName
                }
              }"
            }""";

    @Inject
    TestChargerFactory testChargerFactory;

    @Test
    @DisplayName("Should return charging station with basic fields")
    public void testGetChargingStationQuery() {
        Integer existingChargingStationId = -1;
        given()
                .contentType(ContentType.JSON)
                .body(String.format(CHARGING_STATION_BASIC_QUERY, existingChargingStationId))
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.chargingStation", notNullValue())
                .body("data.chargingStation.id", is(existingChargingStationId.toString()))
                .body("data.chargingStation.name", is("CP0"))
                .body("data.chargingStation.status", is(ChargingStationStatus.Offline.name()))
                .body("data.chargingStation.availabilityStatus", is(AvailabilityStatus.Unknown.name()));
    }

    @Test
    @DisplayName("Should return null for non-existing charging station")
    public void testGetChargingStationQueryNotFound() {
        given()
                .contentType(ContentType.JSON)
                .body(String.format(CHARGING_STATION_SIMPLE_QUERY, 999999))
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.chargingStation", nullValue());
    }

    @Test
    @DisplayName("Should return charging station with evse details")
    public void testGetChargingStationWithEvses() {
        Integer existingChargingStationId = -1;
        given()
                .contentType(ContentType.JSON)
                .body(String.format(CHARGING_STATION_WITH_EVSES_QUERY, existingChargingStationId))
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.chargingStation", notNullValue())
                .body("data.chargingStation.id", is(existingChargingStationId.toString()))
                .body("data.chargingStation.name", is("CP0"))
                .body("data.chargingStation.evses", hasSize(1))
                .body("data.chargingStation.evses[0].id", is("-1"))
                .body("data.chargingStation.evses[0].chargingStationEvseId", is(1))
                .body("data.chargingStation.evses[0].status", is(EvseStatus.Available.name()))
                .body("data.chargingStation.evses[0].availabilityStatus", is(AvailabilityStatus.Operative.name()));
    }

    @Test
    @DisplayName("Should return all charging stations")
    public void testGetChargingStationsBasicQuery() {
        given()
                .contentType(ContentType.JSON)
                .body(CHARGING_STATIONS_BASIC_QUERY)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.chargingStations", notNullValue())
                .body("data.chargingStations.size()", greaterThanOrEqualTo(2))
                .body("data.chargingStations[0].id", is("-1"))
                .body("data.chargingStations[0].name", is("CP0"))
                .body("data.chargingStations[1].id", is("-2"))
                .body("data.chargingStations[1].name", is("CP1"));
    }

    @Test
    @DisplayName("Should return charging stations filtered by powerGridId")
    public void testGetChargingStationsWithPowerGridFilter() {
        String filterQuery = String.format(CHARGING_STATIONS_WITH_FILTER_QUERY, "powerGridId:-1");
        given()
                .contentType(ContentType.JSON)
                .body(filterQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.chargingStations", notNullValue())
                .body("data.chargingStations.size()", greaterThanOrEqualTo(2))
                .body("data.chargingStations[0].id", is("-1"))
                .body("data.chargingStations[0].name", is("CP0"))
                .body("data.chargingStations[1].id", is("-2"))
                .body("data.chargingStations[1].name", is("CP1"));
    }

    @Test
    @DisplayName("Should return empty list for non-existing powerGridId filter")
    public void testGetChargingStationsWithNonExistingPowerGridFilter() {
        String filterQuery = String.format(CHARGING_STATIONS_WITH_FILTER_QUERY, "powerGridId:999");
        given()
                .contentType(ContentType.JSON)
                .body(filterQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.chargingStations", notNullValue())
                .body("data.chargingStations", hasSize(0));
    }

    @Test
    @DisplayName("Should return charging stations with nested relationships")
    public void testGetChargingStationsWithNestedFields() {
        given()
                .contentType(ContentType.JSON)
                .body(CHARGING_STATIONS_WITH_NESTED_QUERY)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.chargingStations", notNullValue())
                .body("data.chargingStations.size()", greaterThanOrEqualTo(2))
                .body("data.chargingStations[0].id", is("-1"))
                .body("data.chargingStations[0].name", is("CP0"))
                .body("data.chargingStations[0].evses", hasSize(1))
                .body("data.chargingStations[0].evses[0].id", is("-1"))
                .body("data.chargingStations[0].evses[0].chargingStationEvseId", is(1));
    }

    @Test
    @DisplayName("Should return charging stations with locationId filter")
    public void testGetChargingStationsWithLocationFilter() {
        String filterQuery = String.format(CHARGING_STATIONS_WITH_FILTER_QUERY, "locationId:-1");
        given()
                .contentType(ContentType.JSON)
                .body(filterQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.chargingStations", notNullValue())
                .body("data.chargingStations.size()", greaterThanOrEqualTo(2))
                .body("data.chargingStations[0].id", is("-1"))
                .body("data.chargingStations[1].id", is("-2"));
    }

    @Test
    @DisplayName("Should return charging stations with combined filters")
    public void testGetChargingStationsWithCombinedFilters() {
        String filterQuery = String.format(CHARGING_STATIONS_WITH_FILTER_QUERY, "locationId:-1,powerGridId:-1");
        given()
                .contentType(ContentType.JSON)
                .body(filterQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.chargingStations", notNullValue())
                .body("data.chargingStations.size()", greaterThanOrEqualTo(2));
    }

    @Test
    @DisplayName("Should return all charging stations when empty filter provided")
    public void testGetChargingStationsWithEmptyFilter() {
        String filterQuery = String.format(CHARGING_STATIONS_WITH_FILTER_QUERY, "");
        given()
                .contentType(ContentType.JSON)
                .body(filterQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.chargingStations", notNullValue())
                .body("data.chargingStations.size()", greaterThanOrEqualTo(2));
    }

    @Test
    @DisplayName("Should register a new charging station with connection url and password")
    public void testRegisterChargingStationBasic() {
        String newChargingStationName = "TestStation1";
        String mutationQuery = String.format(REGISTER_CHARGING_STATION_MUTATION, newChargingStationName);

        try {
        given()
                .contentType(ContentType.JSON)
                .body(mutationQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.registerChargingStation", notNullValue())
                .body("data.registerChargingStation.id", notNullValue())
                .body("data.registerChargingStation.name", is(newChargingStationName))
                .body("data.registerChargingStation.status", is(ChargingStationStatus.New.name()))
                .body("data.registerChargingStation.availabilityStatus", is(AvailabilityStatus.Unknown.name()))
                .body("data.registerChargingStation.password", notNullValue())
                .body("data.registerChargingStation.url", notNullValue());
        } finally {
            DatabaseResetUtil.resetDatabase();
        }
    }

    @Test
    @DisplayName("Should register charging station and verify it can be queried")
    public void testRegisterChargingStationAndQuery() {
        // First, register a new charging station
        String newChargingStationName = "QueryTestStation";
        String mutationQuery = String.format(REGISTER_CHARGING_STATION_MUTATION, newChargingStationName);

        try {
            String newStationId = given()
                    .contentType(ContentType.JSON)
                    .body(mutationQuery)
                    .when()
                    .post(GRAPHQL_ENDPOINT)
                    .then()
                    .statusCode(HttpResponseStatus.OK.code())
                    .body("errors", nullValue())
                    .body("data.registerChargingStation.name", is(newChargingStationName))
                    .extract()
                    .path("data.registerChargingStation.id");

            // Then, query the newly created charging station
            String queryString = String.format(CHARGING_STATION_SIMPLE_QUERY, Long.parseLong(newStationId));
            given()
                    .contentType(ContentType.JSON)
                    .body(queryString)
                    .when()
                    .post(GRAPHQL_ENDPOINT)
                    .then()
                    .statusCode(HttpResponseStatus.OK.code())
                    .body("errors", nullValue())
                    .body("data.chargingStation", notNullValue())
                    .body("data.chargingStation.id", is(newStationId))
                    .body("data.chargingStation.name", is(newChargingStationName));
        } finally {
            DatabaseResetUtil.resetDatabase();
        }
    }

    @Test
    @DisplayName("Should update existing charging station name successfully")
    public void testUpdateChargingStationBasic() {
        Integer existingChargingStationId = -1;
        String newName = "UpdatedCP0";
        String mutationQuery = String.format(UPDATE_CHARGING_STATION_MUTATION, existingChargingStationId, newName);

        try {
            given()
                    .contentType(ContentType.JSON)
                    .body(mutationQuery)
                    .when()
                    .post(GRAPHQL_ENDPOINT)
                    .then()
                    .statusCode(HttpResponseStatus.OK.code())
                    .body("errors", nullValue())
                    .body("data.updateChargingStation", notNullValue())
                    .body("data.updateChargingStation.id", is(existingChargingStationId.toString()))
                    .body("data.updateChargingStation.name", is(newName));
        } finally {
            DatabaseResetUtil.resetDatabase();
        }
    }

    @Test
    @DisplayName("Should fail to update non-existing charging station")
    public void testUpdateNonExistingChargingStation() {
        Integer nonExistingId = 999999;
        String newName = "NonExistentStation";
        String mutationQuery = String.format(UPDATE_CHARGING_STATION_MUTATION, nonExistingId, newName);

        given()
                .contentType(ContentType.JSON)
                .body(mutationQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", notNullValue())
                .body("errors.size()", greaterThanOrEqualTo(1))
                .body("errors[0].message", containsString("Charging Station with id: " + nonExistingId + " does not exists."))
                .body("data.updateChargingStation", nullValue());
    }

    @Test
    @DisplayName("Should fail to change availability when charging station is not connected")
    public void testChangeAvailabilityNoAdapterConnected() {
        Integer existingChargingStationId = -1;
        String mutationQuery = String.format(CHANGE_AVAILABILITY_MUTATION,
                existingChargingStationId, "null", "null", AvailabilityStatus.Operative.name());

        given()
                .contentType(ContentType.JSON)
                .body(mutationQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", notNullValue())
                .body("errors.size()", greaterThanOrEqualTo(1))
                .body("errors[0].message", containsString("Adapter for '" + existingChargingStationId + "' Charging Station not found!"))
                .body("data.changeAvailability", nullValue());
    }

    @Test
    @DisplayName("Should fail to change availability for specific EVSE when charging station is not connected")
    public void testChangeAvailabilityForEvseNoAdapter() {
        Integer existingChargingStationId = -1;
        Integer evseId = 1;
        String mutationQuery = String.format(CHANGE_AVAILABILITY_MUTATION,
                existingChargingStationId, evseId, "null", AvailabilityStatus.Inoperative.name());

        given()
                .contentType(ContentType.JSON)
                .body(mutationQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", notNullValue())
                .body("errors.size()", greaterThanOrEqualTo(1))
                .body("errors[0].message", containsString("Adapter for '" + existingChargingStationId + "' Charging Station not found!"))
                .body("data.changeAvailability", nullValue());
    }

    @Test
    @DisplayName("Should fail to change availability for specific connector when charging station is not connected")
    public void testChangeAvailabilityForConnectorNoAdapter() {
        Integer existingChargingStationId = -1;
        Integer evseId = 1;
        Integer connectorId = 1;
        String mutationQuery = String.format(CHANGE_AVAILABILITY_MUTATION,
                existingChargingStationId, evseId, connectorId, AvailabilityStatus.Operative.name());

        given()
                .contentType(ContentType.JSON)
                .body(mutationQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", notNullValue())
                .body("errors.size()", greaterThanOrEqualTo(1))
                .body("errors[0].message", containsString("Adapter for '" + existingChargingStationId + "' Charging Station not found!"))
                .body("data.changeAvailability", nullValue());
    }

    @Test
    @DisplayName("Should fail to change availability for non-existing charging station")
    public void testChangeAvailabilityNonExistingChargingStation() {
        Integer nonExistingId = 999999;
        String mutationQuery = String.format(CHANGE_AVAILABILITY_MUTATION,
                nonExistingId, "null", "null", AvailabilityStatus.Operative.name());

        given()
                .contentType(ContentType.JSON)
                .body(mutationQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", notNullValue())
                .body("errors.size()", greaterThanOrEqualTo(1))
                .body("errors[0].message", containsString("Adapter for '" + nonExistingId + "' Charging Station not found!"))
                .body("data.changeAvailability", nullValue());
    }

    @Test
    @DisplayName("Should change Availability of a connected charging station")
    public void testChangeAvailability() throws Throwable {
        try {
            var testCharger = VertxContextSupport.subscribeAndAwait(() -> testChargerFactory.createTestCharger(OCPPVersion.OCPP_1_6, "CP00"));
            testCharger.connect();
            testCharger.setAutoResponse(true);

            Integer existingChargingStationId = Math.toIntExact(testCharger.getChargingStationId());

            String operativeMutation = String.format(CHANGE_AVAILABILITY_MUTATION,
                    existingChargingStationId, "null", "1", AvailabilityStatus.Operative.name());

            given()
                    .contentType(ContentType.JSON)
                    .body(operativeMutation)
                    .when()
                    .post(GRAPHQL_ENDPOINT)
                    .then()
                    .statusCode(HttpResponseStatus.OK.code())
                    .body("errors", nullValue())
                    .body("data.changeAvailability", notNullValue())
                    .body("data.changeAvailability.resultStatus", is("Accepted"));
        } finally {
            DatabaseResetUtil.resetDatabase();
        }
    }

    @Test
    @DisplayName("Should fail to reset when charging station is not connected")
    public void testResetNoAdapterConnected() {
        Integer existingChargingStationId = -1;
        String mutationQuery = String.format(RESET_MUTATION,
                existingChargingStationId, "null", ResetType.Immediate.name());

        given()
                .contentType(ContentType.JSON)
                .body(mutationQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", notNullValue())
                .body("errors.size()", greaterThanOrEqualTo(1))
                .body("errors[0].message", containsString("Adapter for '" + existingChargingStationId + "' Charging Station not found!"))
                .body("data.reset", nullValue());
    }

    @Test
    @DisplayName("Should fail to reset specific EVSE when charging station is not connected")
    public void testResetForEvseNoAdapter() {
        Integer existingChargingStationId = -1;
        Integer evseId = 1;
        String mutationQuery = String.format(RESET_MUTATION,
                existingChargingStationId, evseId, ResetType.OnIdle.name());

        given()
                .contentType(ContentType.JSON)
                .body(mutationQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", notNullValue())
                .body("errors.size()", greaterThanOrEqualTo(1))
                .body("errors[0].message", containsString("Adapter for '" + existingChargingStationId + "' Charging Station not found!"))
                .body("data.reset", nullValue());
    }

    @Test
    @DisplayName("Should fail to reset non-existing charging station")
    public void testResetNonExistingChargingStation() {
        Integer nonExistingId = 999999;
        String mutationQuery = String.format(RESET_MUTATION,
                nonExistingId, "null", ResetType.Immediate.name());

        given()
                .contentType(ContentType.JSON)
                .body(mutationQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", notNullValue())
                .body("errors.size()", greaterThanOrEqualTo(1))
                .body("errors[0].message", containsString("Adapter for '" + nonExistingId + "' Charging Station not found!"))
                .body("data.reset", nullValue());
    }

    @Test
    @DisplayName("Should reset a connected charging station with Immediate type")
    public void testResetChargingStationImmediate() throws Throwable {
        try {
            var testCharger = VertxContextSupport.subscribeAndAwait(() -> testChargerFactory.createTestCharger(OCPPVersion.OCPP_1_6, "CP00"));
            testCharger.connect();
            testCharger.setAutoResponse(true);

            Integer existingChargingStationId = Math.toIntExact(testCharger.getChargingStationId());

            String resetMutation = String.format(RESET_MUTATION,
                    existingChargingStationId, "null", ResetType.Immediate.name());

            given()
                    .contentType(ContentType.JSON)
                    .body(resetMutation)
                    .when()
                    .post(GRAPHQL_ENDPOINT)
                    .then()
                    .statusCode(HttpResponseStatus.OK.code())
                    .body("errors", nullValue())
                    .body("data.reset", notNullValue())
                    .body("data.reset.resetStatus", is(ResetStatus.Accepted.name()));
        } finally {
            DatabaseResetUtil.resetDatabase();
        }
    }


    @Test
    @DisplayName("Should start a transaction on a connected charging station")
    public void testRequestStartTransactionSuccess() throws Throwable {
        try {
            var testCharger = VertxContextSupport.subscribeAndAwait(() -> testChargerFactory.createTestCharger(OCPPVersion.OCPP_1_6, "CP00"));
            testCharger.connect();
            testCharger.setAutoResponse(true);

            Integer existingChargingStationId = Math.toIntExact(testCharger.getChargingStationId());
            Integer evseId = 1;
            String mutation = String.format(REQUEST_START_TRANSACTION_MUTATION, existingChargingStationId, evseId);

            given()
                    .contentType(ContentType.JSON)
                    .body(mutation)
                    .when()
                    .post(GRAPHQL_ENDPOINT)
                    .then()
                    .statusCode(HttpResponseStatus.OK.code())
                    .body("errors", nullValue())
                    .body("data.requestStartTransaction", notNullValue())
                    .body("data.requestStartTransaction.status", is(Accepted.name()));
        } finally {
            DatabaseResetUtil.resetDatabase();
        }
    }

    @Test
    @DisplayName("Should fail to start transaction on non-existing charging station")
    public void testRequestStartTransactionNonExistingStation() {
        Integer nonExistingId = 999999;
        Integer evseId = 1;
        String mutation = String.format(REQUEST_START_TRANSACTION_MUTATION, nonExistingId, evseId);

        given()
                .contentType(ContentType.JSON)
                .body(mutation)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", notNullValue())
                .body("errors[0].message", containsString("Adapter for '" + nonExistingId + "' Charging Station not found!"))
                .body("data.requestStartTransaction", nullValue());
    }

    @Test
    @DisplayName("Should successfully stop a transaction with a valid transaction ID")
    public void testRequestStopTransactionActiveTransaction() throws Throwable {
        try {
            var testCharger = VertxContextSupport.subscribeAndAwait(() -> testChargerFactory.createTestCharger(OCPPVersion.OCPP_1_6, "CP_STOP"));
            testCharger.connect();
            testCharger.setAutoResponse(true);

            Integer chargingStationId = Math.toIntExact(testCharger.getChargingStationId());
            Integer transactionId = 1;

            String stopMutation = String.format(REQUEST_STOP_TRANSACTION_MUTATION, chargingStationId, transactionId);
            given()
                    .contentType(ContentType.JSON)
                    .body(stopMutation)
                    .when()
                    .post(GRAPHQL_ENDPOINT)
                    .then()
                    .statusCode(HttpResponseStatus.OK.code())
                    .body("errors", nullValue())
                    .body("data.requestStopTransaction", notNullValue())
                    .body("data.requestStopTransaction.status", is(RequestStartStopTransactionStatus.Accepted.name()));
        } finally {
            DatabaseResetUtil.resetDatabase();
        }
    }

    @Test
    @DisplayName("Should fail to stop a transaction when required parameters (id or transactionId) are null")
    public void testRequestStopTransactionNullParameters() {
        // Case 1: id is null
        String mutationNullId = """
                {
                  "query": "mutation {
                                requestStopTransaction(id: null, transactionId: \\"1\\") {
                                  status
                                }
                              }"
                }""";
        given()
                .contentType(ContentType.JSON)
                .body(mutationNullId)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", notNullValue())
                .body("errors.size()", greaterThanOrEqualTo(1))
                .body("errors[0].message", containsString("Null value for non-null field argument 'id'"))
                .body("data.requestStopTransaction", nullValue());

        // Case 2: transactionId is null
        String mutationNullTxId = """
                {
                  "query": "mutation {
                                requestStopTransaction(id: -1, transactionId: null) {
                                  status
                                }
                              }"
                }""";
        given()
                .contentType(ContentType.JSON)
                .body(mutationNullTxId)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", notNullValue())
                .body("errors.size()", greaterThanOrEqualTo(1))
                .body("errors[0].message", containsString("Null value for non-null field argument 'transactionId'"))
                .body("data.requestStopTransaction", nullValue());

    }

    @Test
    @DisplayName("Should fail to get diagnostics when charging station is not connected")
    public void testGetDiagnosticsNoAdapterConnected() {
        Integer existingChargingStationId = -1;
        String location = "ftp://admin:admin@*************";
        String queryString = String.format(DIAGNOSTICS_QUERY, existingChargingStationId, location, "");

        given()
                .contentType(ContentType.JSON)
                .body(queryString)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", notNullValue())
                .body("errors.size()", greaterThanOrEqualTo(1))
                .body("errors[0].message", containsString("Adapter for '" + existingChargingStationId + "' Charging Station not found!"))
                .body("data.diagnostics", nullValue());
    }

    @Test
    @DisplayName("Should fail to get diagnostics for non-existing charging station")
    public void testGetDiagnosticsNonExistingChargingStation() {
        Integer nonExistingId = 999999;
        String location = "ftp://admin:admin@*************";
        String queryString = String.format(DIAGNOSTICS_QUERY, nonExistingId, location, "");

        given()
                .contentType(ContentType.JSON)
                .body(queryString)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", notNullValue())
                .body("errors.size()", greaterThanOrEqualTo(1))
                .body("errors[0].message", containsString("Adapter for '" + nonExistingId + "' Charging Station not found!"))
                .body("data.diagnostics", nullValue());
    }

    @Test
    @DisplayName("Should get diagnostics from a connected charging station with basic parameters")
    public void testGetDiagnosticsBasic() throws Throwable {
        try {
            var testCharger = VertxContextSupport.subscribeAndAwait(() -> testChargerFactory.createTestCharger(OCPPVersion.OCPP_1_6, "CP00"));
            testCharger.connect();
            testCharger.setAutoResponse(true);

            Integer existingChargingStationId = Math.toIntExact(testCharger.getChargingStationId());
            String location = "ftp://admin:admin@*************";
            String queryString = String.format(DIAGNOSTICS_QUERY, existingChargingStationId, location, "");

            given()
                    .contentType(ContentType.JSON)
                    .body(queryString)
                    .when()
                    .post(GRAPHQL_ENDPOINT)
                    .then()
                    .statusCode(HttpResponseStatus.OK.code())
                    .body("errors", nullValue())
                    .body("data.diagnostics", notNullValue())
                    .body("data.diagnostics.fileName", is("diagnostics.zip"));
        } finally {
            DatabaseResetUtil.resetDatabase();
        }
    }


    @Test
    @DisplayName("Should fail to get diagnostics from OCPP 2.0.1 charging station (not implemented)")
    public void testGetDiagnosticsOcpp201NotImplemented() throws Throwable {
        try {
            var testCharger = VertxContextSupport.subscribeAndAwait(() -> testChargerFactory.createTestCharger(OCPPVersion.OCPP_2_0_1, "CP00"));
            testCharger.connect();
            testCharger.setAutoResponse(true);

            Integer existingChargingStationId = Math.toIntExact(testCharger.getChargingStationId());
            String location = "ftp://admin:admin@*************";
            String queryString = String.format(DIAGNOSTICS_QUERY, existingChargingStationId, location, "");

            given()
                    .contentType(ContentType.JSON)
                    .body(queryString)
                    .when()
                    .post(GRAPHQL_ENDPOINT)
                    .then()
                    .statusCode(HttpResponseStatus.OK.code())
                    .body("errors", notNullValue())
                    .body("errors.size()", greaterThanOrEqualTo(1))
                    .body("errors[0].message", containsString("Not implemented in ocpp 2.0.1"))
                    .body("data.diagnostics", nullValue());
        } finally {
            DatabaseResetUtil.resetDatabase();
        }
    }

}