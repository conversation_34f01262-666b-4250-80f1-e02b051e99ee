0000000000000000000000000000000000000000 86d0d6eaeca74990897b5065df4a29449de5a1f0 malloc <a@a.a> 1721664101 +0200	clone: from gitlab.com:galiasystems/smart-charge.git
86d0d6eaeca74990897b5065df4a29449de5a1f0 d7c4e3bfb40ad36a682dae1d50152cb4499dac2b malloc <a@a.a> 1722146982 +0200	merge origin/main: Fast-forward
d7c4e3bfb40ad36a682dae1d50152cb4499dac2b d43ff38a9ad4b588b5b0d4ae3bd86dc62395743d malloc <a@a.a> 1723489420 +0200	merge origin/main: Fast-forward
d43ff38a9ad4b588b5b0d4ae3bd86dc62395743d 2b8badee0b550e9598f75279fd562f153c105e6b malloc <a@a.a> 1723668158 +0200	merge origin/main: Fast-forward
2b8badee0b550e9598f75279fd562f153c105e6b 2b3234acb376800b673760f8b79ef51b27be1668 malloc <a@a.a> 1724166433 +0200	merge origin/main: Fast-forward
2b3234acb376800b673760f8b79ef51b27be1668 7370e8e0b6bbae40edca7a4d89065825631b0119 malloc <a@a.a> 1724783129 +0200	checkout: moving from main to feat/add-new-entities
7370e8e0b6bbae40edca7a4d89065825631b0119 2b3234acb376800b673760f8b79ef51b27be1668 malloc <a@a.a> 1724785853 +0200	checkout: moving from feat/add-new-entities to main
2b3234acb376800b673760f8b79ef51b27be1668 7370e8e0b6bbae40edca7a4d89065825631b0119 malloc <a@a.a> 1724786900 +0200	checkout: moving from main to feat/add-new-entities
7370e8e0b6bbae40edca7a4d89065825631b0119 a42486c09d10eed008c58ac2fe9890d31e105edf malloc <a@a.a> 1724905454 +0200	checkout: moving from feat/add-new-entities to feature/create_charging_station
a42486c09d10eed008c58ac2fe9890d31e105edf 3c890653a109ab977fd80481cfbb2c119de546fa malloc <a@a.a> 1724962724 +0200	commit: feat: CS register mutation, password generation
3c890653a109ab977fd80481cfbb2c119de546fa 14a587be6354bba3ebd1dc4aebe7c06061ed8922 malloc <a@a.a> 1725479775 +0200	commit: feat: failing create user
14a587be6354bba3ebd1dc4aebe7c06061ed8922 7117c0a75735d932273424564fd71461e131cdc3 malloc <a@a.a> 1725711687 +0200	commit (merge): Merge remote-tracking branch 'refs/remotes/origin/main' into feature/create_charging_station
7117c0a75735d932273424564fd71461e131cdc3 f66607cd25fdfadd4ba6c42f03310024c02e7b4e malloc <a@a.a> 1725718070 +0200	commit: feat: update user
f66607cd25fdfadd4ba6c42f03310024c02e7b4e 0da599810910d5ffaa5b5bd29d1c4cd0dae8092d malloc <a@a.a> 1725718245 +0200	commit (merge): Merge remote-tracking branch 'origin/feature/create_charging_station' into feature/create_charging_station
0da599810910d5ffaa5b5bd29d1c4cd0dae8092d 7d5c4b28ed952d43654f14a7dcc09e29d08cdfe2 malloc <a@a.a> 1725718423 +0200	commit (merge): Merge remote-tracking branch 'refs/remotes/origin/main' into feature/create_charging_station
7d5c4b28ed952d43654f14a7dcc09e29d08cdfe2 393ff794e9ddfa6c29197f527737ccec4f9d880b malloc <a@a.a> 1725741094 +0200	commit: feat: charging station id + creation
393ff794e9ddfa6c29197f527737ccec4f9d880b aff94d51c9810802b95714f1139225ca5aa69965 malloc <a@a.a> 1725804465 +0200	commit: feat: charging station id + creation
aff94d51c9810802b95714f1139225ca5aa69965 38dffa5a92251d4ab328134cb61006339cb817aa malloc <a@a.a> 1725818769 +0200	checkout: moving from feature/create_charging_station to main
38dffa5a92251d4ab328134cb61006339cb817aa f6d9023a42902b7a0428974fe6129885a1ee3171 malloc <a@a.a> 1725818780 +0200	rebase (start): checkout origin/main
f6d9023a42902b7a0428974fe6129885a1ee3171 f6d9023a42902b7a0428974fe6129885a1ee3171 malloc <a@a.a> 1725818780 +0200	rebase (finish): returning to refs/heads/main
f6d9023a42902b7a0428974fe6129885a1ee3171 f6d9023a42902b7a0428974fe6129885a1ee3171 malloc <a@a.a> 1725818819 +0200	checkout: moving from main to feat/register_charging_station
f6d9023a42902b7a0428974fe6129885a1ee3171 51a5ed1baa6232d9a6fd79fc3673c95149ff2f11 malloc <a@a.a> 1725911744 +0200	commit: fix: review
51a5ed1baa6232d9a6fd79fc3673c95149ff2f11 7f8b91e01eb30a590b3ac086a6d2aca347bfb85a malloc <a@a.a> 1725999155 +0200	commit: feat: createChargingStationUser
7f8b91e01eb30a590b3ac086a6d2aca347bfb85a 501a4a539f8a64a72097019c04ca412e715946bd malloc <a@a.a> 1726080273 +0200	rebase (start): checkout origin/feat/register_charging_station
501a4a539f8a64a72097019c04ca412e715946bd 501a4a539f8a64a72097019c04ca412e715946bd malloc <a@a.a> 1726080273 +0200	rebase (finish): returning to refs/heads/feat/register_charging_station
501a4a539f8a64a72097019c04ca412e715946bd 9576d26558cbd3381c5f90f1b425d6f962db3324 malloc <a@a.a> 1726173569 +0200	commit: fix: create and save url for CS
9576d26558cbd3381c5f90f1b425d6f962db3324 358521ec30966ec815b53a0803d4dca42abafc16 malloc <a@a.a> 1726174025 +0200	commit: fix: delete sout
358521ec30966ec815b53a0803d4dca42abafc16 f6d9023a42902b7a0428974fe6129885a1ee3171 malloc <a@a.a> 1726590733 +0200	checkout: moving from feat/register_charging_station to main
f6d9023a42902b7a0428974fe6129885a1ee3171 5dafbc8f09d47a21c75dedbe1ad93841f1f3c282 malloc <a@a.a> 1726590741 +0200	rebase (start): checkout origin/main
5dafbc8f09d47a21c75dedbe1ad93841f1f3c282 5dafbc8f09d47a21c75dedbe1ad93841f1f3c282 malloc <a@a.a> 1726590741 +0200	rebase (finish): returning to refs/heads/main
5dafbc8f09d47a21c75dedbe1ad93841f1f3c282 6251c0c14ce90741f947f085e2b86fe8da75a75e malloc <a@a.a> 1730704610 +0100	rebase (start): checkout origin/main
6251c0c14ce90741f947f085e2b86fe8da75a75e 6251c0c14ce90741f947f085e2b86fe8da75a75e malloc <a@a.a> 1730704610 +0100	rebase (finish): returning to refs/heads/main
6251c0c14ce90741f947f085e2b86fe8da75a75e 6251c0c14ce90741f947f085e2b86fe8da75a75e malloc <a@a.a> 1731421874 +0100	checkout: moving from main to feat/update_charging_station
6251c0c14ce90741f947f085e2b86fe8da75a75e c3ce5d6559a8c6447a26fe6bf3e32c06437c100b malloc <a@a.a> 1731421919 +0100	commit: feat: update charging station gql endpoint
c3ce5d6559a8c6447a26fe6bf3e32c06437c100b 98ea3dfad7b6c86fc155487ed6e40d1fe703592d malloc <a@a.a> 1731516469 +0100	commit: feat: update cs user's password
98ea3dfad7b6c86fc155487ed6e40d1fe703592d 6251c0c14ce90741f947f085e2b86fe8da75a75e malloc <a@a.a> 1731516493 +0100	checkout: moving from feat/update_charging_station to main
6251c0c14ce90741f947f085e2b86fe8da75a75e 98ea3dfad7b6c86fc155487ed6e40d1fe703592d malloc <a@a.a> 1731516827 +0100	checkout: moving from main to feat/update_charging_station
98ea3dfad7b6c86fc155487ed6e40d1fe703592d bc90a6940eacac4519a1edd74ea068365b0f112a malloc <a@a.a> 1731518523 +0100	commit: fix: clean up
bc90a6940eacac4519a1edd74ea068365b0f112a 910525d42d7531caee17f54d98d9767ef7b71954 malloc <a@a.a> 1731518765 +0100	commit: fix: clean up
910525d42d7531caee17f54d98d9767ef7b71954 6251c0c14ce90741f947f085e2b86fe8da75a75e malloc <a@a.a> 1732116886 +0100	checkout: moving from feat/update_charging_station to fix/typos
6251c0c14ce90741f947f085e2b86fe8da75a75e fd79097f5fd1aa83d6c9459c0376b2d277f576d4 malloc <a@a.a> 1732116910 +0100	commit: fix: typos
fd79097f5fd1aa83d6c9459c0376b2d277f576d4 6251c0c14ce90741f947f085e2b86fe8da75a75e malloc <a@a.a> 1732192402 +0100	checkout: moving from fix/typos to main
6251c0c14ce90741f947f085e2b86fe8da75a75e 1c11e36b4f90e0ccc45192f847fb5314196b844e malloc <a@a.a> 1732192430 +0100	rebase (start): checkout origin/main
1c11e36b4f90e0ccc45192f847fb5314196b844e 1c11e36b4f90e0ccc45192f847fb5314196b844e malloc <a@a.a> 1732192430 +0100	rebase (finish): returning to refs/heads/main
1c11e36b4f90e0ccc45192f847fb5314196b844e 1c11e36b4f90e0ccc45192f847fb5314196b844e malloc <a@a.a> 1732810759 +0100	checkout: moving from main to fix/db_init
1c11e36b4f90e0ccc45192f847fb5314196b844e 2065b9d630f241c793b926460e68b47257a898b4 malloc <a@a.a> 1732810826 +0100	commit: fix: ChargingStation role user for example CS
2065b9d630f241c793b926460e68b47257a898b4 1c11e36b4f90e0ccc45192f847fb5314196b844e malloc <a@a.a> 1735652174 +0100	checkout: moving from fix/db_init to main
1c11e36b4f90e0ccc45192f847fb5314196b844e f55d57d4cb27cb8b83692befdd121fc1a54baf05 malloc <a@a.a> 1735652245 +0100	rebase (start): checkout origin/main
f55d57d4cb27cb8b83692befdd121fc1a54baf05 f55d57d4cb27cb8b83692befdd121fc1a54baf05 malloc <a@a.a> 1735652245 +0100	rebase (finish): returning to refs/heads/main
f55d57d4cb27cb8b83692befdd121fc1a54baf05 85a8ac719ab1ce2df5eec8daf45ff0c7aac80091 malloc <a@a.a> 1735758995 +0100	rebase (start): checkout origin/main
85a8ac719ab1ce2df5eec8daf45ff0c7aac80091 85a8ac719ab1ce2df5eec8daf45ff0c7aac80091 malloc <a@a.a> 1735758995 +0100	rebase (finish): returning to refs/heads/main
85a8ac719ab1ce2df5eec8daf45ff0c7aac80091 8e9edef4976b2f4667ba146999a0c68a3a49f579 malloc <a@a.a> 1736699966 +0100	rebase (start): checkout origin/main
8e9edef4976b2f4667ba146999a0c68a3a49f579 8e9edef4976b2f4667ba146999a0c68a3a49f579 malloc <a@a.a> 1736699966 +0100	rebase (finish): returning to refs/heads/main
8e9edef4976b2f4667ba146999a0c68a3a49f579 8e9edef4976b2f4667ba146999a0c68a3a49f579 malloc <a@a.a> 1737459969 +0100	checkout: moving from main to feat/subprotocol_handling
8e9edef4976b2f4667ba146999a0c68a3a49f579 e39342c4c25bde3378f8d00ccb72be1a30c0bbf1 malloc <a@a.a> 1737460463 +0100	commit: feat: handle websocket subprotocol
e39342c4c25bde3378f8d00ccb72be1a30c0bbf1 13f27ab5f8df08da2ef52e57299590365805d074 malloc <a@a.a> 1737488850 +0100	commit: feat: subprotocol handling
13f27ab5f8df08da2ef52e57299590365805d074 0f2a0a16f35e105413384cf760e75576eda61cb0 malloc <a@a.a> 1737810537 +0100	commit: feat: eliminate sec profiles
0f2a0a16f35e105413384cf760e75576eda61cb0 3a8f8405d8b37dc9c6655d0f68aa82dd93f90a7e malloc <a@a.a> 1737820040 +0100	commit: feat: log security profile
3a8f8405d8b37dc9c6655d0f68aa82dd93f90a7e 4b170d6a37fc3308f8f8f99c40f8025f388dc084 malloc <a@a.a> 1737831226 +0100	commit: feat: add todo
4b170d6a37fc3308f8f8f99c40f8025f388dc084 540a63d86bdda7042bc00b581042b8b3432214a1 malloc <a@a.a> 1737894439 +0100	checkout: moving from feat/subprotocol_handling to main
540a63d86bdda7042bc00b581042b8b3432214a1 2d9b3c017bd808c7480b9b068f88fcf705a37479 malloc <a@a.a> 1737898583 +0100	merge origin/main: Fast-forward
2d9b3c017bd808c7480b9b068f88fcf705a37479 66434d2663be3988950a2e35f912ffa8701ff480 malloc <a@a.a> 1737898855 +0100	merge origin/main: Fast-forward
66434d2663be3988950a2e35f912ffa8701ff480 66434d2663be3988950a2e35f912ffa8701ff480 malloc <a@a.a> 1737898877 +0100	checkout: moving from main to feature/bootnotification_1_6
66434d2663be3988950a2e35f912ffa8701ff480 6e5572a83ca651694b7f9434ad431f73c972f1d1 malloc <a@a.a> 1737906456 +0100	commit: feat: BootNotification ocpp1.6
6e5572a83ca651694b7f9434ad431f73c972f1d1 62a2406c3c5b44c28a939f6f996dc70928f1be9f malloc <a@a.a> 1737906571 +0100	commit (merge): Merge branch 'refs/heads/main' into feature/bootnotification_1_6
62a2406c3c5b44c28a939f6f996dc70928f1be9f e4bed36861ec54baed742a58f620af11676571fe malloc <a@a.a> 1737982644 +0100	checkout: moving from feature/bootnotification_1_6 to main
e4bed36861ec54baed742a58f620af11676571fe 690560a4f1dedb6aa861df4d7fe28f839031b4bf malloc <a@a.a> 1737982660 +0100	merge origin/main: Fast-forward
690560a4f1dedb6aa861df4d7fe28f839031b4bf 690560a4f1dedb6aa861df4d7fe28f839031b4bf malloc <a@a.a> 1737982704 +0100	checkout: moving from main to feature/StartTransaction_v1.6
690560a4f1dedb6aa861df4d7fe28f839031b4bf 5b0e2552714e04a86fb101f62532ede12f8c78f5 malloc <a@a.a> 1738170107 +0100	commit: feat: start transaction ocpp1.6
5b0e2552714e04a86fb101f62532ede12f8c78f5 4c437e1e05efb46c18f45da13848494f4b81ca74 malloc <a@a.a> 1738185894 +0100	commit: feat: start transaction ocpp1.6
4c437e1e05efb46c18f45da13848494f4b81ca74 19b0b0e33b467870db062c0c9f20ea98287745e3 malloc <a@a.a> 1738331453 +0100	commit: feat: stop transaction ocpp1.6
19b0b0e33b467870db062c0c9f20ea98287745e3 9c0f02adc53f17ee180cc839375f3cab97b3386f malloc <a@a.a> 1738676577 +0100	commit: fix: review
9c0f02adc53f17ee180cc839375f3cab97b3386f 690560a4f1dedb6aa861df4d7fe28f839031b4bf malloc <a@a.a> 1738676760 +0100	checkout: moving from feature/StartTransaction_v1.6 to main
690560a4f1dedb6aa861df4d7fe28f839031b4bf 9c0f02adc53f17ee180cc839375f3cab97b3386f malloc <a@a.a> 1738678941 +0100	checkout: moving from main to feature/StartTransaction_v1.6
9c0f02adc53f17ee180cc839375f3cab97b3386f ec631109cbbb17e3305759a523ca2ab366d4dc0d malloc <a@a.a> 1738691261 +0100	commit: fix: no evse id in transaction message
ec631109cbbb17e3305759a523ca2ab366d4dc0d 6f9ba867cf542ae1cde222fb18be9a63c4f4ad74 malloc <a@a.a> 1738767354 +0100	commit: fix: transactionId for ocpp1.6 from database sequence
6f9ba867cf542ae1cde222fb18be9a63c4f4ad74 690560a4f1dedb6aa861df4d7fe28f839031b4bf malloc <a@a.a> 1738824880 +0100	checkout: moving from feature/StartTransaction_v1.6 to main
690560a4f1dedb6aa861df4d7fe28f839031b4bf 2585ba22790b15c7ee297848c72418d631c5bdb8 malloc <a@a.a> 1738824897 +0100	merge origin/main: Fast-forward
2585ba22790b15c7ee297848c72418d631c5bdb8 2585ba22790b15c7ee297848c72418d631c5bdb8 malloc <a@a.a> 1738824917 +0100	checkout: moving from main to feature/remote_transaction
2585ba22790b15c7ee297848c72418d631c5bdb8 3d0f75c770203db7120df9ac76a7c526033edbe2 malloc <a@a.a> 1738863039 +0100	commit: feat: remote start transaction
3d0f75c770203db7120df9ac76a7c526033edbe2 fc8f5c2c4e9fdda5424d46601d0054b3c8bd4918 malloc <a@a.a> 1738934426 +0100	commit: feat: remote stop transaction
fc8f5c2c4e9fdda5424d46601d0054b3c8bd4918 00c8f303df35f38a73af60b731036988820c18f0 malloc <a@a.a> 1738946754 +0100	commit: fix: package
00c8f303df35f38a73af60b731036988820c18f0 2585ba22790b15c7ee297848c72418d631c5bdb8 malloc <a@a.a> 1739125190 +0100	checkout: moving from feature/remote_transaction to main
2585ba22790b15c7ee297848c72418d631c5bdb8 1ad68b4557643d1dddfbe65e01c6c5761f4d5559 malloc <a@a.a> 1739125200 +0100	merge origin/main: Fast-forward
1ad68b4557643d1dddfbe65e01c6c5761f4d5559 1ad68b4557643d1dddfbe65e01c6c5761f4d5559 malloc <a@a.a> 1739125235 +0100	checkout: moving from main to feature/remote_start_stop_transaction
1ad68b4557643d1dddfbe65e01c6c5761f4d5559 604b13f504ea655d0cbc93a2efcb33483a35b2fc malloc <a@a.a> 1739207751 +0100	commit: feat: remote start/stop transaction
604b13f504ea655d0cbc93a2efcb33483a35b2fc **************************************** malloc <a@a.a> 1739292788 +0100	checkout: moving from feature/remote_start_stop_transaction to feature/sonar
**************************************** **************************************** malloc <a@a.a> 1739309051 +0100	commit: fix: message id + sonar
**************************************** 7932752251d5585b3073ce41ea731de42d30c57e malloc <a@a.a> 1739349072 +0100	commit: fix: IdBaseEntity
7932752251d5585b3073ce41ea731de42d30c57e 596ac2f2beedd8c7c72a5ad2bfea967ba0549c9a malloc <a@a.a> 1739349198 +0100	commit: fix: IdBaseEntity
596ac2f2beedd8c7c72a5ad2bfea967ba0549c9a **************************************** malloc <a@a.a> 1739360220 +0100	checkout: moving from feature/sonar to feature/meter_values
**************************************** c638057a5d2fa41a81d3671c1a6ec769b1fc8ec5 malloc <a@a.a> 1739903291 +0100	commit: feat: meter values
c638057a5d2fa41a81d3671c1a6ec769b1fc8ec5 9a10b8109f348c6a3fec46e41df76cc9b7142c06 malloc <a@a.a> 1739951878 +0100	checkout: moving from feature/meter_values to fix/meter_values
9a10b8109f348c6a3fec46e41df76cc9b7142c06 bb0ffcdf0ea4e6b63704124756496da7743d91cd malloc <a@a.a> 1739951906 +0100	commit: fix: meter values
bb0ffcdf0ea4e6b63704124756496da7743d91cd 897d6d2eeab0eb2f84bc689fd1ad0e28b6c2f1eb malloc <a@a.a> 1739952078 +0100	commit: fix: meter values
897d6d2eeab0eb2f84bc689fd1ad0e28b6c2f1eb 4090a7423e67640db468b7d38dc36189c79f1acd malloc <a@a.a> 1739971679 +0100	checkout: moving from fix/meter_values to feature/change_configuration
4090a7423e67640db468b7d38dc36189c79f1acd d3f56c2735f38b7c12bba38b9e2040f19925aaa7 malloc <a@a.a> 1739998151 +0100	commit: feat: change configuration
d3f56c2735f38b7c12bba38b9e2040f19925aaa7 1993bc00c8cc910b860d69734b1b2448fc5fdfe7 malloc <a@a.a> 1740007389 +0100	commit: fix: null unit
1993bc00c8cc910b860d69734b1b2448fc5fdfe7 8cb34a2a08bf18e9228da8e954357e14ec6a5f36 malloc <a@a.a> 1740058908 +0100	commit: fix: missing bru
8cb34a2a08bf18e9228da8e954357e14ec6a5f36 8cb34a2a08bf18e9228da8e954357e14ec6a5f36 malloc <a@a.a> 1740059096 +0100	checkout: moving from feature/change_configuration to feature/authorization_ocpp1_6
8cb34a2a08bf18e9228da8e954357e14ec6a5f36 dac6af96b20eedfa2daeb52440ad911d704273ea malloc <a@a.a> 1740064100 +0100	commit: feat: authorize ocpp v1.6
dac6af96b20eedfa2daeb52440ad911d704273ea dac6af96b20eedfa2daeb52440ad911d704273ea malloc <a@a.a> 1740133367 +0100	checkout: moving from feature/authorization_ocpp1_6 to feature/authorization_ocpp2_0_1
dac6af96b20eedfa2daeb52440ad911d704273ea afa8aa914144ad8a8179a7fc1e367d75ba848ac4 malloc <a@a.a> 1740144197 +0100	commit: feat: authorize ocpp v2.0.1
afa8aa914144ad8a8179a7fc1e367d75ba848ac4 879219dd9787b62de5acf5bc4ab63e36eff138fb malloc <a@a.a> 1740167461 +0100	checkout: moving from feature/authorization_ocpp2_0_1 to main
879219dd9787b62de5acf5bc4ab63e36eff138fb 9ab5c93ca48170327e718fc50b2f6e80239eec37 malloc <a@a.a> 1740917998 +0100	merge origin/main: Fast-forward
9ab5c93ca48170327e718fc50b2f6e80239eec37 9ab5c93ca48170327e718fc50b2f6e80239eec37 malloc <a@a.a> 1740918727 +0100	checkout: moving from main to feature/add_charging_station_admin_to_new_cs
9ab5c93ca48170327e718fc50b2f6e80239eec37 ee154c9296ae2b9b6a9db1c33aebec919a12ec6f malloc <a@a.a> 1740921883 +0100	commit: feat: add current user as admin for newly created CS
ee154c9296ae2b9b6a9db1c33aebec919a12ec6f fb870cb77e5a9ab5c155cb6ead5721fad793ebc4 malloc <a@a.a> 1740926975 +0100	commit: fix: extend CsmsServiceBase
fb870cb77e5a9ab5c155cb6ead5721fad793ebc4 d8cd0da2d05366f70881212f26c3acbd68efe951 malloc <a@a.a> 1740938969 +0100	checkout: moving from feature/add_charging_station_admin_to_new_cs to main
d8cd0da2d05366f70881212f26c3acbd68efe951 6b097b224391d70385d7136475d49b465df76829 malloc <a@a.a> 1741080573 +0100	merge origin/main: Fast-forward
6b097b224391d70385d7136475d49b465df76829 6b097b224391d70385d7136475d49b465df76829 malloc <a@a.a> 1741082174 +0100	checkout: moving from main to feature/update_location
6b097b224391d70385d7136475d49b465df76829 ee99446b3390048a3a0c351356ba9bc164b68054 malloc <a@a.a> 1741082297 +0100	commit: feat: update location
ee99446b3390048a3a0c351356ba9bc164b68054 6f27093d18963ef0c145a962c339f377d4ac556f malloc <a@a.a> 1741089282 +0100	commit: fix: persist
6f27093d18963ef0c145a962c339f377d4ac556f 3250a15edd420fcaf0c78445293f6168f2ed7314 malloc <a@a.a> 1741091671 +0100	commit: fix: no persist needed for managed entity
3250a15edd420fcaf0c78445293f6168f2ed7314 a113fa8c7592704a8056803be75fbdd17cf6fde8 malloc <a@a.a> 1741804922 +0100	checkout: moving from feature/update_location to fix/typo
a113fa8c7592704a8056803be75fbdd17cf6fde8 95ce0f588da433ab18bb8200b1403b63593c4738 malloc <a@a.a> 1741804992 +0100	commit: fix: typo
95ce0f588da433ab18bb8200b1403b63593c4738 a113fa8c7592704a8056803be75fbdd17cf6fde8 malloc <a@a.a> 1742454253 +0100	checkout: moving from fix/typo to main
a113fa8c7592704a8056803be75fbdd17cf6fde8 0db9f00fb8f2dd942ec497c855106826b9b2a601 malloc <a@a.a> 1742454273 +0100	merge origin/main: Fast-forward
0db9f00fb8f2dd942ec497c855106826b9b2a601 0db9f00fb8f2dd942ec497c855106826b9b2a601 malloc <a@a.a> 1742454417 +0100	checkout: moving from main to feat/getDiagnostics
0db9f00fb8f2dd942ec497c855106826b9b2a601 cc53dc420fdcd013cda6ea9346b5adce6d3af3d3 malloc <a@a.a> 1742486551 +0100	commit: feat: getDiagnostics
cc53dc420fdcd013cda6ea9346b5adce6d3af3d3 5c1f43d790873deabd703f9c2b688d57714225e5 malloc <a@a.a> 1742543966 +0100	commit: feat: add example getDiagnostics
5c1f43d790873deabd703f9c2b688d57714225e5 b1d248313019f68b801919d37f72afc935710055 malloc <a@a.a> 1742914458 +0100	commit: fix: bru update
b1d248313019f68b801919d37f72afc935710055 5a3d141d6f3adffb506b0d277a5ef8d971921104 malloc <a@a.a> 1743015663 +0100	commit: feat: add TriggerMessage
5a3d141d6f3adffb506b0d277a5ef8d971921104 0db9f00fb8f2dd942ec497c855106826b9b2a601 malloc <a@a.a> 1743168626 +0100	checkout: moving from feat/getDiagnostics to feat/data_from_bootnotification
0db9f00fb8f2dd942ec497c855106826b9b2a601 d7a8f7f06fa7674aa52018695c74e199202b7f36 malloc <a@a.a> 1743168666 +0100	commit: feat: add BootNotification data to ChargingStation
d7a8f7f06fa7674aa52018695c74e199202b7f36 a60a34077e9ca553140b06cb178fe59bf6f29196 malloc <a@a.a> 1743175636 +0100	commit: feat: save adapter type to ChargingStation
a60a34077e9ca553140b06cb178fe59bf6f29196 635e2bcdc46f7d6b618e1ec7fae37da2a2c4fc0a malloc <a@a.a> 1743177408 +0100	commit: feat: ChargingStation builder
635e2bcdc46f7d6b618e1ec7fae37da2a2c4fc0a 0b7473b88945f12ebc98e5d4158f42ab034b96c4 malloc <a@a.a> 1743178297 +0100	commit: feat: save adapter type for ocpp 2.0.1
0b7473b88945f12ebc98e5d4158f42ab034b96c4 4572c0d6ff19e6fcde15ed822e133d1b1ffda44b malloc <a@a.a> 1743179211 +0100	commit: fix: graphql mapper
4572c0d6ff19e6fcde15ed822e133d1b1ffda44b 0db9f00fb8f2dd942ec497c855106826b9b2a601 malloc <a@a.a> 1743415763 +0200	checkout: moving from feat/data_from_bootnotification to main
0db9f00fb8f2dd942ec497c855106826b9b2a601 eb85d24fee247c743e30724c32bcf9cf150b2a0c malloc <a@a.a> 1743415784 +0200	merge origin/main: Fast-forward
eb85d24fee247c743e30724c32bcf9cf150b2a0c eb85d24fee247c743e30724c32bcf9cf150b2a0c malloc <a@a.a> 1743457175 +0200	checkout: moving from main to feature/status_update
eb85d24fee247c743e30724c32bcf9cf150b2a0c 0000000000000000000000000000000000000000 malloc <a@a.a> 1743913082 +0200	Branch: renamed refs/heads/feature/status_update to refs/heads/feature/status_update_gql_subscription
0000000000000000000000000000000000000000 eb85d24fee247c743e30724c32bcf9cf150b2a0c malloc <a@a.a> 1743913082 +0200	Branch: renamed refs/heads/feature/status_update to refs/heads/feature/status_update_gql_subscription
eb85d24fee247c743e30724c32bcf9cf150b2a0c e501bf047e3d3c49005e68c7d5c3f924647b8b10 malloc <a@a.a> 1743913120 +0200	commit: feat: status update with graphql subscription
e501bf047e3d3c49005e68c7d5c3f924647b8b10 7e7bf1188834dae6578e61e5184bf66af41fc848 malloc <a@a.a> 1743913156 +0200	commit: feat: status update with graphql subscription
7e7bf1188834dae6578e61e5184bf66af41fc848 eb85d24fee247c743e30724c32bcf9cf150b2a0c malloc <a@a.a> 1743913722 +0200	checkout: moving from feature/status_update_gql_subscription to main
eb85d24fee247c743e30724c32bcf9cf150b2a0c c70a3feb9836f46d1cfce7acb0c790ac2157cf44 malloc <a@a.a> 1743913737 +0200	merge origin/main: Fast-forward
c70a3feb9836f46d1cfce7acb0c790ac2157cf44 c70a3feb9836f46d1cfce7acb0c790ac2157cf44 malloc <a@a.a> 1744034236 +0200	checkout: moving from main to fix/meter_values_fix
c70a3feb9836f46d1cfce7acb0c790ac2157cf44 cb5eb8c75c0a948f345766acb2ca2255a23d5003 malloc <a@a.a> 1744040074 +0200	commit: fix: MeterValue save
cb5eb8c75c0a948f345766acb2ca2255a23d5003 c70a3feb9836f46d1cfce7acb0c790ac2157cf44 malloc <a@a.a> 1744048214 +0200	checkout: moving from fix/meter_values_fix to main
c70a3feb9836f46d1cfce7acb0c790ac2157cf44 fec2ae5bae0ee14506f38bf584f90ee4e49600d7 malloc <a@a.a> 1744048223 +0200	merge origin/main: Fast-forward
fec2ae5bae0ee14506f38bf584f90ee4e49600d7 fec2ae5bae0ee14506f38bf584f90ee4e49600d7 malloc <a@a.a> 1744048257 +0200	checkout: moving from main to feature/charging_stations_by_location
fec2ae5bae0ee14506f38bf584f90ee4e49600d7 9f59590769c6fd816cfeb6292e78597b983e514c malloc <a@a.a> 1744102556 +0200	commit: feat: charging station filtering by location and power grid
9f59590769c6fd816cfeb6292e78597b983e514c fec2ae5bae0ee14506f38bf584f90ee4e49600d7 malloc <a@a.a> 1744180628 +0200	checkout: moving from feature/charging_stations_by_location to main
fec2ae5bae0ee14506f38bf584f90ee4e49600d7 f46b33acdbd8fce138fda3f39dbf9b1e41af5ec9 malloc <a@a.a> 1744180635 +0200	merge origin/main: Fast-forward
f46b33acdbd8fce138fda3f39dbf9b1e41af5ec9 f46b33acdbd8fce138fda3f39dbf9b1e41af5ec9 malloc <a@a.a> 1744180665 +0200	checkout: moving from main to feature/meter_values_2
f46b33acdbd8fce138fda3f39dbf9b1e41af5ec9 61644bfee66ec56a1d73d94e85d6a2bb55e02cc7 malloc <a@a.a> 1744365490 +0200	commit: feat: gql for MeterValues
61644bfee66ec56a1d73d94e85d6a2bb55e02cc7 d640eb95e8112c96546ecba17644b1eeb02cc860 malloc <a@a.a> 1744366849 +0200	commit: feat: refactor
d640eb95e8112c96546ecba17644b1eeb02cc860 79874774609d448880890a270dd3594fccad6c0a malloc <a@a.a> 1744376705 +0200	commit: fix: test data
79874774609d448880890a270dd3594fccad6c0a f46b33acdbd8fce138fda3f39dbf9b1e41af5ec9 malloc <a@a.a> 1744538947 +0200	checkout: moving from feature/meter_values_2 to main
f46b33acdbd8fce138fda3f39dbf9b1e41af5ec9 402976077cc6b94551241e597fbc4374a76ddf9d malloc <a@a.a> 1744538958 +0200	merge origin/main: Fast-forward
402976077cc6b94551241e597fbc4374a76ddf9d 402976077cc6b94551241e597fbc4374a76ddf9d malloc <a@a.a> 1744538979 +0200	checkout: moving from main to feature/transaction
402976077cc6b94551241e597fbc4374a76ddf9d e594d47b6a8cb6a7ac9822ad9250a9a8828105b4 malloc <a@a.a> 1744638264 +0200	commit: feat: enhance remote start transaction
e594d47b6a8cb6a7ac9822ad9250a9a8828105b4 66acd1caeb7d98d1bca80de23b787cfe9c9c2223 malloc <a@a.a> 1744643928 +0200	commit: feat: fix remote start transaction
66acd1caeb7d98d1bca80de23b787cfe9c9c2223 963c1388fec47221466a8275ce87b0e22061dd9c malloc <a@a.a> 1744732837 +0200	commit: feat: status and idTag added to EvseTransaction
963c1388fec47221466a8275ce87b0e22061dd9c f1016dbccd76e1d42a17b2ad29ab7c928dab6a85 malloc <a@a.a> 1744810115 +0200	commit: feat: stop transaction by idTag
f1016dbccd76e1d42a17b2ad29ab7c928dab6a85 402976077cc6b94551241e597fbc4374a76ddf9d malloc <a@a.a> 1744858674 +0200	checkout: moving from feature/transaction to feature/charging_session
402976077cc6b94551241e597fbc4374a76ddf9d 3a5cac4d8524521738705d9389b6709c40844e0d malloc <a@a.a> 1744867747 +0200	commit: feat: create session
3a5cac4d8524521738705d9389b6709c40844e0d 640900ddc498cb96007980ece02882d6669301d1 malloc <a@a.a> 1744893996 +0200	commit: feat: create transaction
640900ddc498cb96007980ece02882d6669301d1 402976077cc6b94551241e597fbc4374a76ddf9d malloc <a@a.a> 1745259813 +0200	checkout: moving from feature/charging_session to main
402976077cc6b94551241e597fbc4374a76ddf9d 2e0ee27644fe57e8a9044e82af2ea05e5e4133c7 malloc <a@a.a> 1745261231 +0200	rebase (start): checkout origin/main
2e0ee27644fe57e8a9044e82af2ea05e5e4133c7 2e0ee27644fe57e8a9044e82af2ea05e5e4133c7 malloc <a@a.a> 1745261231 +0200	rebase (finish): returning to refs/heads/main
2e0ee27644fe57e8a9044e82af2ea05e5e4133c7 640900ddc498cb96007980ece02882d6669301d1 malloc <a@a.a> 1745302861 +0200	checkout: moving from main to feature/charging_session
640900ddc498cb96007980ece02882d6669301d1 94b88bef933ea0bf5ed6f37a9a3f894fed388f61 malloc <a@a.a> 1745310510 +0200	commit (merge): Merge remote-tracking branch 'refs/remotes/origin/main' into feature/charging_session
94b88bef933ea0bf5ed6f37a9a3f894fed388f61 f18ca6d3d15c7e0acb96838bf205ef788572fce4 malloc <a@a.a> 1745320557 +0200	commit: feat: merge
f18ca6d3d15c7e0acb96838bf205ef788572fce4 92fd5f6275ba4a5899d30bcd77ec2496c023a445 malloc <a@a.a> 1745497210 +0200	commit: feat: session p1
92fd5f6275ba4a5899d30bcd77ec2496c023a445 2e0ee27644fe57e8a9044e82af2ea05e5e4133c7 malloc <a@a.a> 1745651662 +0200	checkout: moving from feature/charging_session to feature/ocpi_session
2e0ee27644fe57e8a9044e82af2ea05e5e4133c7 2e0ee27644fe57e8a9044e82af2ea05e5e4133c7 malloc <a@a.a> 1745653856 +0200	reset: moving to 2e0ee27644fe57e8a9044e82af2ea05e5e4133c7
2e0ee27644fe57e8a9044e82af2ea05e5e4133c7 a3f1f30b171018adf9035c9b6dbb1b773b4fc56f malloc <a@a.a> 1745655396 +0200	commit: feat: ocpi session
a3f1f30b171018adf9035c9b6dbb1b773b4fc56f a51e8f236a66b9c3f368a4ab11180c8911a57dd1 malloc <a@a.a> 1745656639 +0200	merge origin/main: Merge made by the 'ort' strategy.
a51e8f236a66b9c3f368a4ab11180c8911a57dd1 44d73b1c8e560d02a200d147924ecde1137d5b68 malloc <a@a.a> 1745657903 +0200	commit: fix: comments (no ocpi specific definition in service layer)
44d73b1c8e560d02a200d147924ecde1137d5b68 27ee934d7169968edaad2f954d38ce2ff93e794e malloc <a@a.a> 1745658947 +0200	checkout: moving from feature/ocpi_session to feature/session_rest
27ee934d7169968edaad2f954d38ce2ff93e794e 43ae3ea114dada91e4835c474050d488a70ff8d9 malloc <a@a.a> 1745689169 +0200	commit: feat: sessions sender rest endpoint
43ae3ea114dada91e4835c474050d488a70ff8d9 0e2e0f2036c512e5a7faa003a8b3af1514b34684 malloc <a@a.a> 1745689234 +0200	merge origin/main: Merge made by the 'ort' strategy.
0e2e0f2036c512e5a7faa003a8b3af1514b34684 00505e49f788c7ae3f8e23ca10c1ef4ef5ff8b12 malloc <a@a.a> 1745691206 +0200	commit: feat: sessions module fix
00505e49f788c7ae3f8e23ca10c1ef4ef5ff8b12 6b0df5e50b913487432f51ed656eb3b387fbd45f malloc <a@a.a> 1745692420 +0200	commit: feat: sessions module open api fix
6b0df5e50b913487432f51ed656eb3b387fbd45f b38ee0018fda74ef1bbfa600f7a2b2802507b339 malloc <a@a.a> 1745692772 +0200	commit: feat: sessions module open api fix
b38ee0018fda74ef1bbfa600f7a2b2802507b339 620960c6eb96d0c1d317e17af5218e4d42a50487 malloc <a@a.a> 1745742615 +0200	checkout: moving from feature/session_rest to feature/sessions_rest_2
620960c6eb96d0c1d317e17af5218e4d42a50487 72bfbe461c18124a3df3a6313fd917e21384dd6d malloc <a@a.a> 1745750560 +0200	commit: feat: sessions module rest types
72bfbe461c18124a3df3a6313fd917e21384dd6d b7574a96e0a2a84a9f65b14e9cdd5a51429cba33 malloc <a@a.a> 1745838787 +0200	checkout: moving from feature/sessions_rest_2 to feature/meter_values_example_data_for_chart
b7574a96e0a2a84a9f65b14e9cdd5a51429cba33 4b96e38847f2c0286b433ca16ea8f3920c75c0f6 malloc <a@a.a> 1745838799 +0200	commit: feat: example data
4b96e38847f2c0286b433ca16ea8f3920c75c0f6 abec2472eda8b4f2812b98be63573149a3e873be malloc <a@a.a> 1745988494 +0200	checkout: moving from feature/meter_values_example_data_for_chart to main
abec2472eda8b4f2812b98be63573149a3e873be 4b96e38847f2c0286b433ca16ea8f3920c75c0f6 malloc <a@a.a> 1745989961 +0200	checkout: moving from main to feature/meter_values_example_data_for_chart
4b96e38847f2c0286b433ca16ea8f3920c75c0f6 abec2472eda8b4f2812b98be63573149a3e873be malloc <a@a.a> 1745990611 +0200	checkout: moving from feature/meter_values_example_data_for_chart to main
abec2472eda8b4f2812b98be63573149a3e873be abec2472eda8b4f2812b98be63573149a3e873be malloc <a@a.a> 1746075946 +0200	checkout: moving from main to feature/typesafe_filtering
abec2472eda8b4f2812b98be63573149a3e873be 85a5e45ae86e3eb1c0dab9e8a5b2f5f2691b2d37 malloc <a@a.a> 1746096192 +0200	commit: feat: type-safe filtering
85a5e45ae86e3eb1c0dab9e8a5b2f5f2691b2d37 abec2472eda8b4f2812b98be63573149a3e873be malloc <a@a.a> 1746631893 +0200	checkout: moving from feature/typesafe_filtering to main
abec2472eda8b4f2812b98be63573149a3e873be 85a5e45ae86e3eb1c0dab9e8a5b2f5f2691b2d37 malloc <a@a.a> 1746956269 +0200	checkout: moving from main to feature/typesafe_filtering
85a5e45ae86e3eb1c0dab9e8a5b2f5f2691b2d37 943a4f4bcff3ca8762e9a94c8ca6e6c414711243 malloc <a@a.a> 1747041185 +0200	commit: feat: use AttributeFilter for generic filtering
943a4f4bcff3ca8762e9a94c8ca6e6c414711243 ac49713f00a4da7070470ed1bb3287a595bb7c93 malloc <a@a.a> 1747120863 +0200	commit: feat: refactor
ac49713f00a4da7070470ed1bb3287a595bb7c93 3b8c0e1d0b3b843b0a35ae53c6bbcb2880c5c561 malloc <a@a.a> 1747125070 +0200	commit: feat: refactor
3b8c0e1d0b3b843b0a35ae53c6bbcb2880c5c561 2ebf19972fb8a409e7ad1f8d7afd3d67a1ebe69a malloc <a@a.a> 1747126553 +0200	commit: feat: refactor CriteriaQueryHelper
2ebf19972fb8a409e7ad1f8d7afd3d67a1ebe69a b660f416a79938d99025a164b89dbfe11b171e80 malloc <a@a.a> 1747127860 +0200	commit: feat: refactor count session
b660f416a79938d99025a164b89dbfe11b171e80 5ff600833a143e2df3717aa11adfd0689ec61936 malloc <a@a.a> 1747128141 +0200	commit: feat: refactor
5ff600833a143e2df3717aa11adfd0689ec61936 abec2472eda8b4f2812b98be63573149a3e873be malloc <a@a.a> 1747131084 +0200	checkout: moving from feature/typesafe_filtering to fix/test
abec2472eda8b4f2812b98be63573149a3e873be abec2472eda8b4f2812b98be63573149a3e873be malloc <a@a.a> 1747511769 +0200	checkout: moving from fix/test to main
abec2472eda8b4f2812b98be63573149a3e873be a6c3e22d5c79a508972d35a2297d99e69dd138cb malloc <a@a.a> 1747511780 +0200	merge origin/main: Fast-forward
a6c3e22d5c79a508972d35a2297d99e69dd138cb abec2472eda8b4f2812b98be63573149a3e873be malloc <a@a.a> 1747567891 +0200	checkout: moving from main to fix/test
abec2472eda8b4f2812b98be63573149a3e873be a6c3e22d5c79a508972d35a2297d99e69dd138cb malloc <a@a.a> 1747567916 +0200	merge origin/main: Fast-forward
a6c3e22d5c79a508972d35a2297d99e69dd138cb 8ef2dcc025563e314028042ee04ca54e700fa87d malloc <a@a.a> 1749571015 +0200	commit: tests with authentication + TestCharger improvement
8ef2dcc025563e314028042ee04ca54e700fa87d 01ac8af9c60e0d15d969fdb2568b83cc19d93440 malloc <a@a.a> 1749572264 +0200	commit: remove report test
01ac8af9c60e0d15d969fdb2568b83cc19d93440 9144b55ea32e04d626babf93ce6d7433ab4dcbfe malloc <a@a.a> 1749646052 +0200	commit: login tests
9144b55ea32e04d626babf93ce6d7433ab4dcbfe d5a4b4603c47e7b68cf6b13d62c2a656cdb7ef33 malloc <a@a.a> 1749649027 +0200	commit: fix login tests
d5a4b4603c47e7b68cf6b13d62c2a656cdb7ef33 72f17e0ee4ab5f514fca3087ec735668bb9798a4 malloc <a@a.a> 1749649222 +0200	commit: fix login tests
72f17e0ee4ab5f514fca3087ec735668bb9798a4 1b8fbe64be9b405ce73499a6b17c1900e4a59845 malloc <a@a.a> 1749653159 +0200	commit: fix login tests
1b8fbe64be9b405ce73499a6b17c1900e4a59845 76141c777b6c2532c6708554943d3a156072021a malloc <a@a.a> 1749712876 +0200	commit: chargingStation query test
76141c777b6c2532c6708554943d3a156072021a 43f32f885330c0286bacd6b3d8fbeb0c5200465d malloc <a@a.a> 1749721986 +0200	commit: chargingStations query + registerChargingStation mutation test
43f32f885330c0286bacd6b3d8fbeb0c5200465d a6c3e22d5c79a508972d35a2297d99e69dd138cb malloc <a@a.a> 1749999516 +0200	checkout: moving from fix/test to main
a6c3e22d5c79a508972d35a2297d99e69dd138cb f180b7968fc84852fc71c5dd5d9613524369ad0a malloc <a@a.a> 1749999540 +0200	rebase (start): checkout origin/main
f180b7968fc84852fc71c5dd5d9613524369ad0a f180b7968fc84852fc71c5dd5d9613524369ad0a malloc <a@a.a> 1749999540 +0200	rebase (finish): returning to refs/heads/main
f180b7968fc84852fc71c5dd5d9613524369ad0a f180b7968fc84852fc71c5dd5d9613524369ad0a malloc <a@a.a> 1750045169 +0200	checkout: moving from main to fix/test2
f180b7968fc84852fc71c5dd5d9613524369ad0a f9f7060eb3bfc55ad1eb46bc45c8d0d22736d883 malloc <a@a.a> 1750047450 +0200	commit: Mutation tests + db reset service for tests
f9f7060eb3bfc55ad1eb46bc45c8d0d22736d883 aac7bd10e292080a13f9714bf330b32b2035e9e4 malloc <a@a.a> 1750061090 +0200	commit: fix db modification tests
aac7bd10e292080a13f9714bf330b32b2035e9e4 8621ce5c84518e747d85f835243757cfdb8fae14 malloc <a@a.a> 1750085343 +0200	commit: change available mutation test
8621ce5c84518e747d85f835243757cfdb8fae14 263342907aac23533b64c3354717af8a572ab328 malloc <a@a.a> 1750096406 +0200	commit: TestCharger auto replay
263342907aac23533b64c3354717af8a572ab328 4789a3b32464acaff3d39c5a1f9fe642270a7ded malloc <a@a.a> 1750099052 +0200	commit: TestCharger refactor
4789a3b32464acaff3d39c5a1f9fe642270a7ded b8dd71dc8a9517f1a98c63ebf4c57acfe98c8b6f malloc <a@a.a> 1750131513 +0200	commit: reset tests
b8dd71dc8a9517f1a98c63ebf4c57acfe98c8b6f a2cb5afed351c430a6b91fb51d90cfb022602229 malloc <a@a.a> 1750136028 +0200	commit: remote request start transaction tests
a2cb5afed351c430a6b91fb51d90cfb022602229 bc436023157e81ffef70ad8c59dae040b6c27422 malloc <a@a.a> 1750140772 +0200	commit: remote request stop transaction tests
bc436023157e81ffef70ad8c59dae040b6c27422 cecc177515612048284eabb37314961d84beaea8 malloc <a@a.a> 1750148278 +0200	commit: getDiagnostic tests
